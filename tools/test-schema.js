#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function testSchema() {
  console.log('🧪 Testing Schema Validation...');
  
  try {
    // Read and parse the schema
    const schemaPath = path.join(__dirname, 'generators/swagger-gen/schema.json');
    const schemaContent = fs.readFileSync(schemaPath, 'utf-8');
    const schema = JSON.parse(schemaContent);
    
    console.log('✅ Schema JSON is valid');
    
    // Check required properties
    if (!schema.$schema) {
      throw new Error('Missing $schema property');
    }
    
    if (!schema.properties) {
      throw new Error('Missing properties object');
    }
    
    console.log('✅ Schema structure is valid');
    
    // Check for problematic patterns that cause Nx Console issues
    const problematicPatterns = [];
    
    function checkProperty(name, prop, path = '') {
      const fullPath = path ? `${path}.${name}` : name;
      
      // Check for complex x-prompt configurations
      if (prop['x-prompt'] && typeof prop['x-prompt'] === 'object') {
        if (prop['x-prompt'].items && typeof prop['x-prompt'].items === 'object') {
          if (prop['x-prompt'].items.$source || prop['x-prompt'].items.filter) {
            problematicPatterns.push(`${fullPath}: Complex x-prompt.items configuration`);
          }
        }
        if (prop['x-prompt'].when) {
          problematicPatterns.push(`${fullPath}: Conditional x-prompt.when`);
        }
      }
      
      // Check for nested objects
      if (prop.properties) {
        Object.entries(prop.properties).forEach(([subName, subProp]) => {
          checkProperty(subName, subProp, fullPath);
        });
      }
    }
    
    // Check all properties
    Object.entries(schema.properties).forEach(([name, prop]) => {
      checkProperty(name, prop);
    });
    
    if (problematicPatterns.length > 0) {
      console.log('⚠️  Found potentially problematic patterns:');
      problematicPatterns.forEach(pattern => {
        console.log(`   - ${pattern}`);
      });
    } else {
      console.log('✅ No problematic patterns found');
    }
    
    // Test basic schema properties
    console.log('📋 Schema Summary:');
    console.log(`   Schema version: ${schema.$schema}`);
    console.log(`   Title: ${schema.title}`);
    console.log(`   Properties: ${Object.keys(schema.properties).length}`);
    console.log(`   Required: ${schema.required ? schema.required.length : 0}`);
    
    // List all properties
    console.log('📝 Properties:');
    Object.entries(schema.properties).forEach(([name, prop]) => {
      const hasPrompt = prop['x-prompt'] ? '📝' : '  ';
      const isRequired = schema.required && schema.required.includes(name) ? '*' : ' ';
      console.log(`   ${hasPrompt}${isRequired} ${name} (${prop.type}): ${prop.description}`);
    });
    
    console.log('✅ Schema validation completed successfully!');
    
  } catch (error) {
    console.error('❌ Schema validation failed:', error.message);
    process.exit(1);
  }
}

testSchema();
