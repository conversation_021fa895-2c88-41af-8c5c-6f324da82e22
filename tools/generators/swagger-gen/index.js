"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const devkit_1 = require("@nx/devkit");
const child_process_1 = require("child_process");
const fs_1 = require("fs");
const path_1 = require("path");
// Additional imports needed for the generator
const { updateProjectConfiguration, ProjectConfiguration, } = require('@nx/devkit');
const { librarySecondaryEntryPointGenerator, } = require('@nx/angular/generators');
async function default_1(tree, options) {
    try {
        devkit_1.logger.info('🚀 Starting Swagger Generator...');
        // Ensure target project is set (with interactive selection if needed)
        if (!options.targetProject) {
            options.targetProject = await selectTargetProject(tree);
        }
        // Validate inputs
        await validateInputs(tree, options);
        // Parse swagger file and extract microservice name
        const microserviceName = await extractMicroserviceName(options.swaggerPath);
        devkit_1.logger.info(`📝 Extracted microservice name: ${microserviceName}`);
        // Get target project configuration
        const projectConfig = (0, devkit_1.readProjectConfiguration)(tree, options.targetProject);
        const projectRoot = projectConfig.root;
        // Check if entry point exists
        const entryPointExists = await checkEntryPointExists(tree, projectRoot, microserviceName);
        if (options.dryRun) {
            await performDryRun(tree, options, microserviceName, entryPointExists);
            return;
        }
        // Create or update entry point
        await ensureEntryPoint(tree, options.targetProject, microserviceName, entryPointExists);
        // Generate swagger files
        await generateSwaggerFiles(tree, options, microserviceName, projectRoot);
        // Update index file
        await updateIndexFile(tree, projectRoot, microserviceName);
        // Update project configuration if needed
        await updateProjectConfig(tree, options.targetProject, microserviceName);
        await (0, devkit_1.formatFiles)(tree);
        devkit_1.logger.info('✅ Swagger generation completed successfully!');
    }
    catch (error) {
        await handleGeneratorError(error, options);
        throw error;
    }
}
exports.default = default_1;
async function selectTargetProject(tree) {
    const projects = (0, devkit_1.getProjects)(tree);
    const libraryProjects = Array.from(projects.entries())
        .filter(([_, config]) => config.projectType === 'library')
        .map(([name, _]) => name)
        .sort();
    if (libraryProjects.length === 0) {
        throw new Error('No library projects found in the workspace');
    }
    // Check if swagger-generator exists and make it the default
    const defaultProject = libraryProjects.includes('swagger-generator')
        ? 'swagger-generator'
        : libraryProjects[0];
    devkit_1.logger.info('📋 Available library projects:');
    libraryProjects.forEach((project, index) => {
        const marker = project === defaultProject ? '❯' : ' ';
        devkit_1.logger.info(`  ${marker} ${project}`);
    });
    // For now, return the default project
    // In a real implementation, you would use inquirer or similar for interactive selection
    devkit_1.logger.info(`🎯 Using default project: ${defaultProject}`);
    return defaultProject;
}
async function validateInputs(tree, options) {
    // Check if swagger file exists
    if (!(0, fs_1.existsSync)(options.swaggerPath)) {
        throw new Error(`Swagger file not found: ${options.swaggerPath}`);
    }
    // Check if target project exists
    const projects = (0, devkit_1.getProjects)(tree);
    if (!projects.has(options.targetProject)) {
        throw new Error(`Project '${options.targetProject}' not found`);
    }
    // Verify it's a library project
    const projectConfig = (0, devkit_1.readProjectConfiguration)(tree, options.targetProject);
    if (projectConfig.projectType !== 'library') {
        throw new Error(`Project '${options.targetProject}' is not a library`);
    }
}
async function extractMicroserviceName(swaggerPath) {
    try {
        const swaggerContent = (0, fs_1.readFileSync)(swaggerPath, 'utf-8');
        const swaggerSpec = JSON.parse(swaggerContent);
        // Validate swagger/openapi version
        if (!swaggerSpec.openapi && !swaggerSpec.swagger) {
            throw new Error('Invalid swagger file: missing openapi or swagger version');
        }
        if (!swaggerSpec.info?.title) {
            throw new Error('Swagger file missing info.title property');
        }
        let title = swaggerSpec.info.title.trim();
        if (!title) {
            throw new Error('Swagger file has empty info.title property');
        }
        devkit_1.logger.info(`📋 Original service title: ${title}`);
        // Remove prefix before first dash if it exists
        const dashIndex = title.indexOf('-');
        if (dashIndex > 0) {
            const originalTitle = title;
            title = title.substring(dashIndex + 1);
            devkit_1.logger.info(`🔧 Removed prefix, new title: ${title} (was: ${originalTitle})`);
        }
        // Clean up the name to be a valid directory name
        const cleanedName = title
            .toLowerCase()
            .replace(/[^a-z0-9-]/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
        if (!cleanedName) {
            throw new Error(`Could not generate valid microservice name from title: ${swaggerSpec.info.title}`);
        }
        return cleanedName;
    }
    catch (error) {
        if (error.code === 'ENOENT') {
            throw new Error(`Swagger file not found: ${swaggerPath}`);
        }
        if (error instanceof SyntaxError) {
            throw new Error(`Invalid JSON in swagger file: ${swaggerPath}`);
        }
        throw new Error(`Failed to parse swagger file: ${error.message}`);
    }
}
async function checkEntryPointExists(tree, projectRoot, microserviceName) {
    const entryPointPath = (0, path_1.join)(projectRoot, microserviceName);
    const ngPackagePath = (0, path_1.join)(entryPointPath, 'ng-package.json');
    const srcPath = (0, path_1.join)(entryPointPath, 'src');
    return (tree.exists(entryPointPath) &&
        tree.exists(ngPackagePath) &&
        tree.exists(srcPath));
}
async function ensureEntryPoint(tree, targetProject, microserviceName, exists) {
    if (!exists) {
        devkit_1.logger.info(`📦 Creating new entry point: ${microserviceName}`);
        await createEntryPoint(tree, targetProject, microserviceName);
    }
    else {
        devkit_1.logger.info(`📦 Entry point already exists: ${microserviceName} (will update)`);
    }
}
async function createEntryPoint(tree, targetProject, microserviceName) {
    // Use Nx generator to create secondary entry point
    await librarySecondaryEntryPointGenerator(tree, {
        name: microserviceName,
        library: targetProject,
        skipModule: true,
    });
}
async function generateSwaggerFiles(tree, options, microserviceName, projectRoot) {
    devkit_1.logger.info(`🔧 Generating swagger files for ${microserviceName}...`);
    const outputDir = (0, path_1.join)(projectRoot, microserviceName, 'src', 'lib');
    const absoluteOutputDir = (0, path_1.join)(process.cwd(), outputDir);
    const absoluteSwaggerPath = (0, path_1.join)(process.cwd(), options.swaggerPath);
    // Ensure output directory exists in the tree
    if (!tree.exists(outputDir)) {
        devkit_1.logger.info(`📁 Creating output directory: ${outputDir}`);
    }
    // Create ng-swagger-gen configuration
    const configPath = (0, path_1.join)(process.cwd(), 'ng-swagger-gen.json');
    const config = {
        input: absoluteSwaggerPath,
        output: absoluteOutputDir,
        ignoreUnusedModels: false,
        removeStaleFiles: true,
        modelIndex: true,
        serviceIndex: true,
        apiModule: true,
        configuration: true,
        baseService: true,
        requestBuilder: false,
        response: true,
    };
    // Write temporary config file
    const configContent = JSON.stringify(config, null, 2);
    require('fs').writeFileSync(configPath, configContent);
    try {
        // Execute ng-swagger-gen with config file
        const command = `npx ng-swagger-gen --config "${configPath}"`;
        devkit_1.logger.info(`📋 Executing: ${command}`);
        (0, child_process_1.execSync)(command, {
            stdio: 'pipe',
            cwd: process.cwd(),
            encoding: 'utf-8',
        });
        devkit_1.logger.info('✅ ng-swagger-gen execution completed');
        // Clean up config file
        require('fs').unlinkSync(configPath);
    }
    catch (error) {
        // Clean up config file on error
        try {
            require('fs').unlinkSync(configPath);
        }
        catch (cleanupError) {
            // Ignore cleanup errors
        }
        devkit_1.logger.error(`❌ ng-swagger-gen failed: ${error.message}`);
        throw new Error(`Failed to execute ng-swagger-gen: ${error.message}`);
    }
}
async function updateIndexFile(tree, projectRoot, microserviceName) {
    const indexPath = (0, path_1.join)(projectRoot, microserviceName, 'src', 'index.ts');
    const libPath = (0, path_1.join)(projectRoot, microserviceName, 'src', 'lib');
    // Validate that the lib directory exists
    if (!tree.exists(libPath)) {
        throw new Error(`Generated lib directory not found: ${libPath}`);
    }
    // Define the required exports
    const requiredExports = [
        'api-configuration',
        'api.module',
        'base-service',
        'models',
        'services',
        'strict-http-response',
    ];
    // Validate that required files exist
    const missingFiles = [];
    for (const exportName of requiredExports) {
        const filePath = (0, path_1.join)(libPath, `${exportName}.ts`);
        if (!tree.exists(filePath)) {
            missingFiles.push(exportName);
        }
    }
    if (missingFiles.length > 0) {
        devkit_1.logger.warn(`⚠️  Some expected files were not generated: ${missingFiles.join(', ')}`);
    }
    // Generate index content with proper exports
    const indexContent = requiredExports
        .map((exportName) => `export * from './lib/${exportName}';`)
        .join('\n') + '\n';
    // Check if index file already exists and compare content
    const existingContent = tree.exists(indexPath)
        ? tree.read(indexPath, 'utf-8')
        : '';
    if (existingContent === indexContent) {
        devkit_1.logger.info(`📝 Index file already up to date: ${indexPath}`);
    }
    else {
        tree.write(indexPath, indexContent);
        devkit_1.logger.info(`📝 ${existingContent ? 'Updated' : 'Created'} index file: ${indexPath}`);
    }
    // Log the exports for verification
    devkit_1.logger.info(`📋 Index file exports:`);
    requiredExports.forEach((exportName) => {
        const exists = !missingFiles.includes(exportName);
        const status = exists ? '✅' : '❌';
        devkit_1.logger.info(`  ${status} ${exportName}`);
    });
}
async function updateProjectConfig(tree, targetProject, microserviceName) {
    const projectConfig = (0, devkit_1.readProjectConfiguration)(tree, targetProject);
    // Check if the project uses ng-packagr (Angular library)
    const buildTarget = projectConfig.targets?.build;
    if (!buildTarget || buildTarget.executor !== '@nx/angular:package') {
        devkit_1.logger.warn(`⚠️  Project '${targetProject}' does not use @nx/angular:package executor`);
        devkit_1.logger.info(`📋 Entry point '${microserviceName}' created in project '${targetProject}'`);
        return;
    }
    // For Angular libraries with ng-packagr, the entry points are automatically
    // discovered based on the directory structure and ng-package.json files
    // No explicit configuration is needed in project.json
    devkit_1.logger.info(`📋 Entry point '${microserviceName}' registered in project '${targetProject}'`);
    devkit_1.logger.info(`   📄 ng-packagr will automatically discover the entry point`);
    devkit_1.logger.info(`   📁 Entry point path: libs/${targetProject}/${microserviceName}`);
    // Verify the ng-package.json structure
    const ngPackagePath = (0, path_1.join)(projectConfig.root, microserviceName, 'ng-package.json');
    if (tree.exists(ngPackagePath)) {
        try {
            const ngPackageContent = tree.read(ngPackagePath, 'utf-8');
            const ngPackageConfig = JSON.parse(ngPackageContent);
            if (ngPackageConfig.lib?.entryFile) {
                devkit_1.logger.info(`   ✅ Entry file configured: ${ngPackageConfig.lib.entryFile}`);
            }
            else {
                devkit_1.logger.warn(`   ⚠️  No entry file configured in ng-package.json`);
            }
        }
        catch (error) {
            devkit_1.logger.warn(`   ⚠️  Could not parse ng-package.json: ${error.message}`);
        }
    }
    else {
        devkit_1.logger.warn(`   ⚠️  ng-package.json not found at ${ngPackagePath}`);
    }
    // Log build information
    devkit_1.logger.info(`   🔧 Build command: nx build ${targetProject}`);
    devkit_1.logger.info(`   📦 Output: dist/libs/${targetProject}/${microserviceName}`);
    devkit_1.logger.info(`   📋 Import path: @${getWorkspaceScope(tree)}/${targetProject}/${microserviceName}`);
}
function getWorkspaceScope(tree) {
    try {
        const packageJsonPath = 'package.json';
        if (tree.exists(packageJsonPath)) {
            const packageJson = JSON.parse(tree.read(packageJsonPath, 'utf-8'));
            const name = packageJson.name;
            if (name && name.startsWith('@')) {
                return name.split('/')[0].substring(1);
            }
        }
    }
    catch (error) {
        // Ignore errors and use default
    }
    return 'workspace';
}
async function handleGeneratorError(error, options) {
    devkit_1.logger.error('❌ Swagger Generator failed');
    devkit_1.logger.error('═'.repeat(50));
    // Categorize and provide helpful error messages
    if (error.message?.includes('Swagger file not found')) {
        devkit_1.logger.error('📄 Swagger File Error:');
        devkit_1.logger.error(`   File not found: ${options.swaggerPath}`);
        devkit_1.logger.error('   💡 Solutions:');
        devkit_1.logger.error('   - Check if the file path is correct');
        devkit_1.logger.error('   - Ensure the file exists and is readable');
        devkit_1.logger.error('   - Use an absolute path if needed');
    }
    else if (error.message?.includes('Invalid JSON')) {
        devkit_1.logger.error('📄 Swagger File Error:');
        devkit_1.logger.error(`   Invalid JSON in file: ${options.swaggerPath}`);
        devkit_1.logger.error('   💡 Solutions:');
        devkit_1.logger.error('   - Validate the JSON syntax');
        devkit_1.logger.error('   - Check for trailing commas or missing quotes');
        devkit_1.logger.error('   - Use a JSON validator tool');
    }
    else if (error.message?.includes('missing info.title')) {
        devkit_1.logger.error('📄 Swagger Specification Error:');
        devkit_1.logger.error('   Missing required info.title property');
        devkit_1.logger.error('   💡 Solutions:');
        devkit_1.logger.error('   - Add info.title to your swagger specification');
        devkit_1.logger.error('   - Ensure the title is not empty');
        devkit_1.logger.error('   - Example: "info": { "title": "My API Service" }');
    }
    else if (error.message?.includes('Project') &&
        error.message?.includes('not found')) {
        devkit_1.logger.error('📦 Project Error:');
        devkit_1.logger.error(`   Target project not found: ${options.targetProject}`);
        devkit_1.logger.error('   💡 Solutions:');
        devkit_1.logger.error('   - Check available projects with: nx show projects');
        devkit_1.logger.error('   - Create the library first: nx generate @nx/angular:library');
        devkit_1.logger.error('   - Use interactive selection by omitting --targetProject');
    }
    else if (error.message?.includes('not a library')) {
        devkit_1.logger.error('📦 Project Type Error:');
        devkit_1.logger.error(`   Project '${options.targetProject}' is not a library`);
        devkit_1.logger.error('   💡 Solutions:');
        devkit_1.logger.error('   - Use a library project instead of an application');
        devkit_1.logger.error('   - Create a new library: nx generate @nx/angular:library');
        devkit_1.logger.error('   - Check project type with: nx show project <name>');
    }
    else if (error.message?.includes('ng-swagger-gen')) {
        devkit_1.logger.error('🔧 Code Generation Error:');
        devkit_1.logger.error('   ng-swagger-gen execution failed');
        devkit_1.logger.error('   💡 Solutions:');
        devkit_1.logger.error('   - Ensure ng-swagger-gen is installed: npm install ng-swagger-gen');
        devkit_1.logger.error('   - Check if the swagger specification is valid');
        devkit_1.logger.error('   - Try running ng-swagger-gen manually to see detailed errors');
        devkit_1.logger.error('   - Check output directory permissions');
    }
    else if (error.message?.includes('ENOENT')) {
        devkit_1.logger.error('📁 File System Error:');
        devkit_1.logger.error('   File or directory not found');
        devkit_1.logger.error('   💡 Solutions:');
        devkit_1.logger.error('   - Check file paths and permissions');
        devkit_1.logger.error('   - Ensure all required directories exist');
        devkit_1.logger.error('   - Run from the workspace root directory');
    }
    else {
        devkit_1.logger.error('🔧 Unexpected Error:');
        devkit_1.logger.error(`   ${error.message}`);
        devkit_1.logger.error('   💡 Solutions:');
        devkit_1.logger.error('   - Check the full error details above');
        devkit_1.logger.error('   - Try running with --verbose for more information');
        devkit_1.logger.error('   - Use --dry-run to preview changes without executing');
    }
    devkit_1.logger.error('');
    devkit_1.logger.error('📋 Debug Information:');
    devkit_1.logger.error(`   Swagger file: ${options.swaggerPath}`);
    devkit_1.logger.error(`   Target project: ${options.targetProject}`);
    devkit_1.logger.error(`   Dry run: ${options.dryRun ? 'Yes' : 'No'}`);
    devkit_1.logger.error('');
    devkit_1.logger.error('🔍 For more help:');
    devkit_1.logger.error('   - Run with --dry-run to preview changes');
    devkit_1.logger.error('   - Check the generator documentation');
    devkit_1.logger.error('   - Validate your swagger file with online tools');
    devkit_1.logger.error('═'.repeat(50));
}
async function performDryRun(tree, options, microserviceName, entryPointExists) {
    devkit_1.logger.info('🔍 DRY RUN MODE - Preview of changes');
    devkit_1.logger.info('═'.repeat(50));
    devkit_1.logger.info('');
    // Show swagger file analysis
    try {
        const swaggerContent = (0, fs_1.readFileSync)(options.swaggerPath, 'utf-8');
        const swaggerSpec = JSON.parse(swaggerContent);
        devkit_1.logger.info('📋 Swagger File Analysis:');
        devkit_1.logger.info(`  📄 File: ${options.swaggerPath}`);
        devkit_1.logger.info(`  📝 Title: ${swaggerSpec.info.title}`);
        devkit_1.logger.info(`  🔢 Version: ${swaggerSpec.info.version || 'N/A'}`);
        devkit_1.logger.info(`  🏷️  Extracted name: ${microserviceName}`);
        devkit_1.logger.info('');
    }
    catch (error) {
        devkit_1.logger.error(`❌ Error analyzing swagger file: ${error.message}`);
        return;
    }
    const projectConfig = (0, devkit_1.readProjectConfiguration)(tree, options.targetProject);
    const projectRoot = projectConfig.root;
    const entryPointPath = (0, path_1.join)(projectRoot, microserviceName);
    const outputDir = (0, path_1.join)(entryPointPath, 'src', 'lib');
    const indexPath = (0, path_1.join)(entryPointPath, 'src', 'index.ts');
    // Show entry point status
    devkit_1.logger.info('📦 Entry Point Status:');
    if (!entryPointExists) {
        devkit_1.logger.info(`  ➕ Would CREATE new entry point: ${microserviceName}`);
        devkit_1.logger.info(`     📁 Location: ${entryPointPath}`);
        devkit_1.logger.info(`     📄 Files to create:`);
        devkit_1.logger.info(`       - ng-package.json`);
        devkit_1.logger.info(`       - src/index.ts`);
        devkit_1.logger.info(`       - src/lib/ (directory)`);
    }
    else {
        devkit_1.logger.info(`  🔄 Would UPDATE existing entry point: ${microserviceName}`);
        devkit_1.logger.info(`     📁 Location: ${entryPointPath}`);
    }
    devkit_1.logger.info('');
    // Show files that would be generated
    devkit_1.logger.info('🔧 Files to be generated by ng-swagger-gen:');
    devkit_1.logger.info(`  📁 Output directory: ${outputDir}`);
    devkit_1.logger.info('  📄 Generated files:');
    const generatedFiles = [
        'api-configuration.ts',
        'api.module.ts',
        'base-service.ts',
        'models.ts',
        'services.ts',
        'strict-http-response.ts',
    ];
    generatedFiles.forEach((file) => {
        const filePath = (0, path_1.join)(outputDir, file);
        const exists = tree.exists(filePath);
        const action = exists ? 'UPDATE' : 'CREATE';
        const icon = exists ? '🔄' : '➕';
        devkit_1.logger.info(`    ${icon} ${action}: ${file}`);
    });
    devkit_1.logger.info('  📁 Generated directories:');
    devkit_1.logger.info('    ➕ models/ (individual model files)');
    devkit_1.logger.info('    ➕ services/ (individual service files)');
    devkit_1.logger.info('');
    // Show index file changes
    devkit_1.logger.info('📝 Index File Changes:');
    const indexExists = tree.exists(indexPath);
    if (indexExists) {
        devkit_1.logger.info(`  🔄 Would UPDATE: ${indexPath}`);
        const currentContent = tree.read(indexPath, 'utf-8') || '';
        const newContent = [
            'api-configuration',
            'api.module',
            'base-service',
            'models',
            'services',
            'strict-http-response',
        ]
            .map((name) => `export * from './lib/${name}';`)
            .join('\n') + '\n';
        if (currentContent !== newContent) {
            devkit_1.logger.info('    📋 Content would change');
        }
        else {
            devkit_1.logger.info('    ✅ Content already up to date');
        }
    }
    else {
        devkit_1.logger.info(`  ➕ Would CREATE: ${indexPath}`);
    }
    devkit_1.logger.info('');
    // Show summary
    devkit_1.logger.info('📊 Summary:');
    devkit_1.logger.info(`  🎯 Target project: ${options.targetProject}`);
    devkit_1.logger.info(`  📦 Entry point: ${microserviceName}`);
    devkit_1.logger.info(`  📄 Swagger file: ${options.swaggerPath}`);
    devkit_1.logger.info('');
    devkit_1.logger.info('💡 To execute these changes, run the command without --dry-run');
    devkit_1.logger.info('═'.repeat(50));
}
