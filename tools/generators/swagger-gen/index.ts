import {
  Tree,
  formatFiles,
  getProjects,
  logger,
  readProjectConfiguration,
} from '@nx/devkit';
import { execSync } from 'child_process';
import { existsSync, readFileSync } from 'fs';
import { join } from 'path';

// Additional imports needed for the generator
const {
  updateProjectConfiguration,
  ProjectConfiguration,
} = require('@nx/devkit');
const {
  librarySecondaryEntryPointGenerator,
} = require('@nx/angular/generators');

export interface SwaggerGenGeneratorSchema {
  swaggerPath: string;
  targetProject?: string;
  dryRun?: boolean;
  force?: boolean;
  skipInstall?: boolean;
  verbose?: boolean;
}

interface SwaggerInfo {
  title: string;
  description?: string;
  version?: string;
}

interface SwaggerSpec {
  info: SwaggerInfo;
  openapi?: string;
  swagger?: string;
}

export default async function (tree: Tree, options: SwaggerGenGeneratorSchema) {
  try {
    if (options.verbose) {
      logger.info('🔧 Verbose mode enabled');
      logger.info(`📋 Generator options: ${JSON.stringify(options, null, 2)}`);
    }

    logger.info('🚀 Starting Swagger Generator...');

    // Ensure target project is set (with interactive selection if needed)
    if (!options.targetProject) {
      options.targetProject = await selectTargetProject(tree);
    }

    if (options.verbose) {
      logger.info(`🎯 Target project: ${options.targetProject}`);
      logger.info(`📄 Swagger file: ${options.swaggerPath}`);
    }

    // Validate inputs
    await validateInputs(tree, options);

    // Parse swagger file and extract microservice name
    const microserviceName = await extractMicroserviceName(options.swaggerPath);
    logger.info(`📝 Extracted microservice name: ${microserviceName}`);

    // Get target project configuration
    const projectConfig = readProjectConfiguration(tree, options.targetProject);
    const projectRoot = projectConfig.root;

    // Check if entry point exists
    const entryPointExists = await checkEntryPointExists(
      tree,
      projectRoot,
      microserviceName,
    );

    if (options.dryRun) {
      await performDryRun(tree, options, microserviceName, entryPointExists);
      return;
    }

    // Create or update entry point
    await ensureEntryPoint(
      tree,
      options.targetProject,
      microserviceName,
      entryPointExists,
    );

    // Generate swagger files
    await generateSwaggerFiles(tree, options, microserviceName, projectRoot);

    // Update index file
    await updateIndexFile(tree, projectRoot, microserviceName);

    // Update project configuration if needed
    await updateProjectConfig(tree, options.targetProject, microserviceName);

    await formatFiles(tree);

    logger.info('✅ Swagger generation completed successfully!');
  } catch (error) {
    await handleGeneratorError(error, options);
    throw error;
  }
}

async function selectTargetProject(tree: Tree): Promise<string> {
  const projects = getProjects(tree);
  const libraryProjects = Array.from(projects.entries())
    .filter(([_, config]) => config.projectType === 'library')
    .map(([name, _]) => name)
    .sort();

  if (libraryProjects.length === 0) {
    throw new Error('No library projects found in the workspace');
  }

  // Check if swagger-generator exists and make it the default
  const defaultProject = libraryProjects.includes('swagger-generator')
    ? 'swagger-generator'
    : libraryProjects[0];

  logger.info('📋 Available library projects:');
  libraryProjects.forEach((project, index) => {
    const marker = project === defaultProject ? '❯' : ' ';
    logger.info(`  ${marker} ${project}`);
  });

  // Try to use inquirer for interactive selection if available
  try {
    const inquirer = require('inquirer');
    const answer = await inquirer.prompt([
      {
        type: 'list',
        name: 'project',
        message: 'Which library project should be used for generation?',
        choices: libraryProjects,
        default: defaultProject,
      },
    ]);
    return answer.project;
  } catch (error) {
    // Fallback to default if inquirer is not available
    logger.info(`🎯 Using default project: ${defaultProject}`);
    return defaultProject;
  }
}

async function validateInputs(
  tree: Tree,
  options: SwaggerGenGeneratorSchema,
): Promise<void> {
  if (options.verbose) {
    logger.info('🔍 Validating inputs...');
  }

  // Check if swagger file exists
  if (!existsSync(options.swaggerPath)) {
    const error = new Error(`Swagger file not found: ${options.swaggerPath}`);
    error.name = 'SwaggerFileNotFound';
    throw error;
  }

  if (options.verbose) {
    logger.info(`✅ Swagger file found: ${options.swaggerPath}`);
  }

  // Check if target project exists
  const projects = getProjects(tree);
  if (!projects.has(options.targetProject!)) {
    const availableLibraries = Array.from(projects.entries())
      .filter(([_, config]) => config.projectType === 'library')
      .map(([name, _]) => name);

    const error = new Error(
      `Project '${options.targetProject}' not found. Available libraries: ${availableLibraries.join(', ')}`,
    );
    error.name = 'ProjectNotFound';
    throw error;
  }

  if (options.verbose) {
    logger.info(`✅ Target project found: ${options.targetProject}`);
  }

  // Verify it's a library project
  const projectConfig = readProjectConfiguration(tree, options.targetProject!);
  if (projectConfig.projectType !== 'library') {
    const error = new Error(
      `Project '${options.targetProject}' is not a library (type: ${projectConfig.projectType})`,
    );
    error.name = 'InvalidProjectType';
    throw error;
  }

  if (options.verbose) {
    logger.info(`✅ Project type validated: library`);
    logger.info(`📁 Project root: ${projectConfig.root}`);
  }
}

async function extractMicroserviceName(swaggerPath: string): Promise<string> {
  try {
    const swaggerContent = readFileSync(swaggerPath, 'utf-8');
    const swaggerSpec: SwaggerSpec = JSON.parse(swaggerContent);

    // Validate swagger/openapi version
    if (!swaggerSpec.openapi && !swaggerSpec.swagger) {
      throw new Error(
        'Invalid swagger file: missing openapi or swagger version',
      );
    }

    if (!swaggerSpec.info?.title) {
      throw new Error('Swagger file missing info.title property');
    }

    let title = swaggerSpec.info.title.trim();

    if (!title) {
      throw new Error('Swagger file has empty info.title property');
    }

    logger.info(`📋 Original service title: ${title}`);

    // Remove prefix before first dash if it exists
    const dashIndex = title.indexOf('-');
    if (dashIndex > 0) {
      const originalTitle = title;
      title = title.substring(dashIndex + 1);
      logger.info(
        `🔧 Removed prefix, new title: ${title} (was: ${originalTitle})`,
      );
    }

    // Clean up the name to be a valid directory name
    const cleanedName = title
      .toLowerCase()
      .replace(/[^a-z0-9-]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');

    if (!cleanedName) {
      throw new Error(
        `Could not generate valid microservice name from title: ${swaggerSpec.info.title}`,
      );
    }

    return cleanedName;
  } catch (error) {
    if (error.code === 'ENOENT') {
      throw new Error(`Swagger file not found: ${swaggerPath}`);
    }
    if (error instanceof SyntaxError) {
      throw new Error(`Invalid JSON in swagger file: ${swaggerPath}`);
    }
    throw new Error(`Failed to parse swagger file: ${error.message}`);
  }
}

async function checkEntryPointExists(
  tree: Tree,
  projectRoot: string,
  microserviceName: string,
): Promise<boolean> {
  const entryPointPath = join(projectRoot, microserviceName);
  const ngPackagePath = join(entryPointPath, 'ng-package.json');
  const srcPath = join(entryPointPath, 'src');

  return (
    tree.exists(entryPointPath) &&
    tree.exists(ngPackagePath) &&
    tree.exists(srcPath)
  );
}

async function ensureEntryPoint(
  tree: Tree,
  targetProject: string,
  microserviceName: string,
  exists: boolean,
): Promise<void> {
  if (!exists) {
    logger.info(`📦 Creating new entry point: ${microserviceName}`);
    await createEntryPoint(tree, targetProject, microserviceName);
  } else {
    logger.info(
      `📦 Entry point already exists: ${microserviceName} (will update)`,
    );
  }
}

async function createEntryPoint(
  tree: Tree,
  targetProject: string,
  microserviceName: string,
): Promise<void> {
  // Use Nx generator to create secondary entry point
  await librarySecondaryEntryPointGenerator(tree, {
    name: microserviceName,
    library: targetProject,
    skipModule: true,
  });
}

async function generateSwaggerFiles(
  tree: Tree,
  options: SwaggerGenGeneratorSchema,
  microserviceName: string,
  projectRoot: string,
): Promise<void> {
  logger.info(`🔧 Generating swagger files for ${microserviceName}...`);

  const outputDir = join(projectRoot, microserviceName, 'src', 'lib');
  const absoluteOutputDir = join(process.cwd(), outputDir);
  const absoluteSwaggerPath = join(process.cwd(), options.swaggerPath);

  // Ensure output directory exists in the tree
  if (!tree.exists(outputDir)) {
    logger.info(`📁 Creating output directory: ${outputDir}`);
  }

  // Create ng-swagger-gen configuration
  const configPath = join(process.cwd(), 'ng-swagger-gen.json');
  const config = {
    input: absoluteSwaggerPath,
    output: absoluteOutputDir,
    ignoreUnusedModels: false,
    removeStaleFiles: true,
    modelIndex: true,
    serviceIndex: true,
    apiModule: true,
    configuration: true,
    baseService: true,
    requestBuilder: false,
    response: true,
  };

  // Write temporary config file
  const configContent = JSON.stringify(config, null, 2);
  require('fs').writeFileSync(configPath, configContent);

  try {
    // Check if ng-swagger-gen is available
    if (!options.skipInstall) {
      try {
        execSync('npx ng-swagger-gen --version', { stdio: 'pipe' });
      } catch (error) {
        logger.info('📦 ng-swagger-gen not found, installing...');
        execSync('npm install ng-swagger-gen', { stdio: 'inherit' });
      }
    }

    // Execute ng-swagger-gen with config file
    const command = `npx ng-swagger-gen --config "${configPath}"`;

    if (options.verbose) {
      logger.info(`📋 Executing: ${command}`);
      logger.info(`📁 Working directory: ${process.cwd()}`);
      logger.info(`⚙️  Configuration: ${JSON.stringify(config, null, 2)}`);
    } else {
      logger.info(`📋 Executing: ${command}`);
    }

    execSync(command, {
      stdio: options.verbose ? 'inherit' : 'pipe',
      cwd: process.cwd(),
      encoding: 'utf-8',
    });

    logger.info('✅ ng-swagger-gen execution completed');

    // Clean up config file
    require('fs').unlinkSync(configPath);
  } catch (error) {
    // Clean up config file on error
    try {
      require('fs').unlinkSync(configPath);
    } catch (cleanupError) {
      // Ignore cleanup errors
    }

    logger.error(`❌ ng-swagger-gen failed: ${error.message}`);
    throw new Error(`Failed to execute ng-swagger-gen: ${error.message}`);
  }
}

async function updateIndexFile(
  tree: Tree,
  projectRoot: string,
  microserviceName: string,
): Promise<void> {
  const indexPath = join(projectRoot, microserviceName, 'src', 'index.ts');
  const libPath = join(projectRoot, microserviceName, 'src', 'lib');

  // Validate that the lib directory exists
  if (!tree.exists(libPath)) {
    throw new Error(`Generated lib directory not found: ${libPath}`);
  }

  // Define the required exports
  const requiredExports = [
    'api-configuration',
    'api.module',
    'base-service',
    'models',
    'services',
    'strict-http-response',
  ];

  // Validate that required files exist
  const missingFiles: string[] = [];
  for (const exportName of requiredExports) {
    const filePath = join(libPath, `${exportName}.ts`);
    if (!tree.exists(filePath)) {
      missingFiles.push(exportName);
    }
  }

  if (missingFiles.length > 0) {
    logger.warn(
      `⚠️  Some expected files were not generated: ${missingFiles.join(', ')}`,
    );
  }

  // Generate index content with proper exports
  const indexContent =
    requiredExports
      .map((exportName) => `export * from './lib/${exportName}';`)
      .join('\n') + '\n';

  // Check if index file already exists and compare content
  const existingContent = tree.exists(indexPath)
    ? tree.read(indexPath, 'utf-8')
    : '';

  if (existingContent === indexContent) {
    logger.info(`📝 Index file already up to date: ${indexPath}`);
  } else {
    tree.write(indexPath, indexContent);
    logger.info(
      `📝 ${existingContent ? 'Updated' : 'Created'} index file: ${indexPath}`,
    );
  }

  // Log the exports for verification
  logger.info(`📋 Index file exports:`);
  requiredExports.forEach((exportName) => {
    const exists = !missingFiles.includes(exportName);
    const status = exists ? '✅' : '❌';
    logger.info(`  ${status} ${exportName}`);
  });
}

async function updateProjectConfig(
  tree: Tree,
  targetProject: string,
  microserviceName: string,
): Promise<void> {
  const projectConfig = readProjectConfiguration(tree, targetProject);

  // Check if the project uses ng-packagr (Angular library)
  const buildTarget = projectConfig.targets?.build;
  if (!buildTarget || buildTarget.executor !== '@nx/angular:package') {
    logger.warn(
      `⚠️  Project '${targetProject}' does not use @nx/angular:package executor`,
    );
    logger.info(
      `📋 Entry point '${microserviceName}' created in project '${targetProject}'`,
    );
    return;
  }

  // For Angular libraries with ng-packagr, the entry points are automatically
  // discovered based on the directory structure and ng-package.json files
  // No explicit configuration is needed in project.json

  logger.info(
    `📋 Entry point '${microserviceName}' registered in project '${targetProject}'`,
  );
  logger.info(`   📄 ng-packagr will automatically discover the entry point`);
  logger.info(
    `   📁 Entry point path: libs/${targetProject}/${microserviceName}`,
  );

  // Verify the ng-package.json structure
  const ngPackagePath = join(
    projectConfig.root,
    microserviceName,
    'ng-package.json',
  );
  if (tree.exists(ngPackagePath)) {
    try {
      const ngPackageContent = tree.read(ngPackagePath, 'utf-8');
      const ngPackageConfig = JSON.parse(ngPackageContent);

      if (ngPackageConfig.lib?.entryFile) {
        logger.info(
          `   ✅ Entry file configured: ${ngPackageConfig.lib.entryFile}`,
        );
      } else {
        logger.warn(`   ⚠️  No entry file configured in ng-package.json`);
      }
    } catch (error) {
      logger.warn(`   ⚠️  Could not parse ng-package.json: ${error.message}`);
    }
  } else {
    logger.warn(`   ⚠️  ng-package.json not found at ${ngPackagePath}`);
  }

  // Log build information
  logger.info(`   🔧 Build command: nx build ${targetProject}`);
  logger.info(`   📦 Output: dist/libs/${targetProject}/${microserviceName}`);
  logger.info(
    `   📋 Import path: @${getWorkspaceScope(tree)}/${targetProject}/${microserviceName}`,
  );
}

function getWorkspaceScope(tree: Tree): string {
  try {
    const packageJsonPath = 'package.json';
    if (tree.exists(packageJsonPath)) {
      const packageJson = JSON.parse(tree.read(packageJsonPath, 'utf-8'));
      const name = packageJson.name;
      if (name && name.startsWith('@')) {
        return name.split('/')[0].substring(1);
      }
    }
  } catch (error) {
    // Ignore errors and use default
  }
  return 'workspace';
}

async function handleGeneratorError(
  error: any,
  options: SwaggerGenGeneratorSchema,
): Promise<void> {
  logger.error('❌ Swagger Generator failed');
  logger.error('═'.repeat(50));

  // Categorize and provide helpful error messages based on error name or message
  if (
    error.name === 'SwaggerFileNotFound' ||
    error.message?.includes('Swagger file not found')
  ) {
    logger.error('📄 Swagger File Error:');
    logger.error(`   File not found: ${options.swaggerPath}`);
    logger.error('   💡 Solutions:');
    logger.error('   - Check if the file path is correct');
    logger.error('   - Ensure the file exists and is readable');
    logger.error('   - Use an absolute path if needed');
  } else if (error.message?.includes('Invalid JSON')) {
    logger.error('📄 Swagger File Error:');
    logger.error(`   Invalid JSON in file: ${options.swaggerPath}`);
    logger.error('   💡 Solutions:');
    logger.error('   - Validate the JSON syntax');
    logger.error('   - Check for trailing commas or missing quotes');
    logger.error('   - Use a JSON validator tool');
  } else if (error.message?.includes('missing info.title')) {
    logger.error('📄 Swagger Specification Error:');
    logger.error('   Missing required info.title property');
    logger.error('   💡 Solutions:');
    logger.error('   - Add info.title to your swagger specification');
    logger.error('   - Ensure the title is not empty');
    logger.error('   - Example: "info": { "title": "My API Service" }');
  } else if (
    error.message?.includes('Project') &&
    error.message?.includes('not found')
  ) {
    logger.error('📦 Project Error:');
    logger.error(`   Target project not found: ${options.targetProject}`);
    logger.error('   💡 Solutions:');
    logger.error('   - Check available projects with: nx show projects');
    logger.error(
      '   - Create the library first: nx generate @nx/angular:library',
    );
    logger.error('   - Use interactive selection by omitting --targetProject');
  } else if (error.message?.includes('not a library')) {
    logger.error('📦 Project Type Error:');
    logger.error(`   Project '${options.targetProject}' is not a library`);
    logger.error('   💡 Solutions:');
    logger.error('   - Use a library project instead of an application');
    logger.error('   - Create a new library: nx generate @nx/angular:library');
    logger.error('   - Check project type with: nx show project <name>');
  } else if (error.message?.includes('ng-swagger-gen')) {
    logger.error('🔧 Code Generation Error:');
    logger.error('   ng-swagger-gen execution failed');
    logger.error('   💡 Solutions:');
    logger.error(
      '   - Ensure ng-swagger-gen is installed: npm install ng-swagger-gen',
    );
    logger.error('   - Check if the swagger specification is valid');
    logger.error(
      '   - Try running ng-swagger-gen manually to see detailed errors',
    );
    logger.error('   - Check output directory permissions');
  } else if (error.message?.includes('ENOENT')) {
    logger.error('📁 File System Error:');
    logger.error('   File or directory not found');
    logger.error('   💡 Solutions:');
    logger.error('   - Check file paths and permissions');
    logger.error('   - Ensure all required directories exist');
    logger.error('   - Run from the workspace root directory');
  } else {
    logger.error('🔧 Unexpected Error:');
    logger.error(`   ${error.message}`);
    logger.error('   💡 Solutions:');
    logger.error('   - Check the full error details above');
    logger.error('   - Try running with --verbose for more information');
    logger.error('   - Use --dry-run to preview changes without executing');
  }

  logger.error('');
  logger.error('📋 Debug Information:');
  logger.error(`   Swagger file: ${options.swaggerPath}`);
  logger.error(`   Target project: ${options.targetProject}`);
  logger.error(`   Dry run: ${options.dryRun ? 'Yes' : 'No'}`);
  logger.error('');
  logger.error('🔍 For more help:');
  logger.error('   - Run with --dry-run to preview changes');
  logger.error('   - Check the generator documentation');
  logger.error('   - Validate your swagger file with online tools');
  logger.error('═'.repeat(50));
}

async function performDryRun(
  tree: Tree,
  options: SwaggerGenGeneratorSchema,
  microserviceName: string,
  entryPointExists: boolean,
): Promise<void> {
  logger.info('🔍 DRY RUN MODE - Preview of changes');
  logger.info('═'.repeat(50));
  logger.info('');

  // Show swagger file analysis
  try {
    const swaggerContent = readFileSync(options.swaggerPath, 'utf-8');
    const swaggerSpec: SwaggerSpec = JSON.parse(swaggerContent);
    logger.info('📋 Swagger File Analysis:');
    logger.info(`  📄 File: ${options.swaggerPath}`);
    logger.info(`  📝 Title: ${swaggerSpec.info.title}`);
    logger.info(`  🔢 Version: ${swaggerSpec.info.version || 'N/A'}`);
    logger.info(`  🏷️  Extracted name: ${microserviceName}`);
    logger.info('');
  } catch (error) {
    logger.error(`❌ Error analyzing swagger file: ${error.message}`);
    return;
  }

  const projectConfig = readProjectConfiguration(tree, options.targetProject);
  const projectRoot = projectConfig.root;
  const entryPointPath = join(projectRoot, microserviceName);
  const outputDir = join(entryPointPath, 'src', 'lib');
  const indexPath = join(entryPointPath, 'src', 'index.ts');

  // Show entry point status
  logger.info('📦 Entry Point Status:');
  if (!entryPointExists) {
    logger.info(`  ➕ Would CREATE new entry point: ${microserviceName}`);
    logger.info(`     📁 Location: ${entryPointPath}`);
    logger.info(`     📄 Files to create:`);
    logger.info(`       - ng-package.json`);
    logger.info(`       - src/index.ts`);
    logger.info(`       - src/lib/ (directory)`);
  } else {
    logger.info(`  🔄 Would UPDATE existing entry point: ${microserviceName}`);
    logger.info(`     📁 Location: ${entryPointPath}`);
  }
  logger.info('');

  // Show files that would be generated
  logger.info('🔧 Files to be generated by ng-swagger-gen:');
  logger.info(`  📁 Output directory: ${outputDir}`);
  logger.info('  📄 Generated files:');

  const generatedFiles = [
    'api-configuration.ts',
    'api.module.ts',
    'base-service.ts',
    'models.ts',
    'services.ts',
    'strict-http-response.ts',
  ];

  generatedFiles.forEach((file) => {
    const filePath = join(outputDir, file);
    const exists = tree.exists(filePath);
    const action = exists ? 'UPDATE' : 'CREATE';
    const icon = exists ? '🔄' : '➕';
    logger.info(`    ${icon} ${action}: ${file}`);
  });

  logger.info('  📁 Generated directories:');
  logger.info('    ➕ models/ (individual model files)');
  logger.info('    ➕ services/ (individual service files)');
  logger.info('');

  // Show index file changes
  logger.info('📝 Index File Changes:');
  const indexExists = tree.exists(indexPath);
  if (indexExists) {
    logger.info(`  🔄 Would UPDATE: ${indexPath}`);
    const currentContent = tree.read(indexPath, 'utf-8') || '';
    const newContent =
      [
        'api-configuration',
        'api.module',
        'base-service',
        'models',
        'services',
        'strict-http-response',
      ]
        .map((name) => `export * from './lib/${name}';`)
        .join('\n') + '\n';

    if (currentContent !== newContent) {
      logger.info('    📋 Content would change');
    } else {
      logger.info('    ✅ Content already up to date');
    }
  } else {
    logger.info(`  ➕ Would CREATE: ${indexPath}`);
  }
  logger.info('');

  // Show summary
  logger.info('📊 Summary:');
  logger.info(`  🎯 Target project: ${options.targetProject}`);
  logger.info(`  📦 Entry point: ${microserviceName}`);
  logger.info(`  📄 Swagger file: ${options.swaggerPath}`);
  logger.info('');
  logger.info('💡 To execute these changes, run the command without --dry-run');
  logger.info('═'.repeat(50));
}
