import {
  Tree,
  formatFiles,
  getProjects,
  logger,
  readProjectConfiguration,
} from '@nx/devkit';
import { execSync } from 'child_process';
import { existsSync, readFileSync } from 'fs';
import { join } from 'path';

// Additional imports needed for the generator
const {
  updateProjectConfiguration,
  ProjectConfiguration,
} = require('@nx/devkit');
const {
  librarySecondaryEntryPointGenerator,
} = require('@nx/angular/generators');

export interface SwaggerGenGeneratorSchema {
  swaggerPath: string;
  targetProject: string;
  dryRun?: boolean;
  force?: boolean;
}

interface SwaggerInfo {
  title: string;
  description?: string;
  version?: string;
}

interface SwaggerSpec {
  info: SwaggerInfo;
  openapi?: string;
  swagger?: string;
}

export default async function (tree: Tree, options: SwaggerGenGeneratorSchema) {
  logger.info('🚀 Starting Swagger Generator...');

  // Ensure target project is set (with interactive selection if needed)
  if (!options.targetProject) {
    options.targetProject = await selectTargetProject(tree);
  }

  // Validate inputs
  await validateInputs(tree, options);

  // Parse swagger file and extract microservice name
  const microserviceName = await extractMicroserviceName(options.swaggerPath);
  logger.info(`📝 Extracted microservice name: ${microserviceName}`);

  // Get target project configuration
  const projectConfig = readProjectConfiguration(tree, options.targetProject);
  const projectRoot = projectConfig.root;

  // Check if entry point exists
  const entryPointPath = join(projectRoot, microserviceName);
  const entryPointExists = tree.exists(entryPointPath);

  if (options.dryRun) {
    await performDryRun(tree, options, microserviceName, entryPointExists);
    return;
  }

  // Create or update entry point
  if (!entryPointExists) {
    await createEntryPoint(tree, options.targetProject, microserviceName);
  }

  // Generate swagger files
  await generateSwaggerFiles(tree, options, microserviceName, projectRoot);

  // Update index file
  await updateIndexFile(tree, projectRoot, microserviceName);

  // Update project configuration if needed
  await updateProjectConfig(tree, options.targetProject, microserviceName);

  await formatFiles(tree);

  logger.info('✅ Swagger generation completed successfully!');
}

async function selectTargetProject(tree: Tree): Promise<string> {
  const projects = getProjects(tree);
  const libraryProjects = Array.from(projects.entries())
    .filter(([_, config]) => config.projectType === 'library')
    .map(([name, _]) => name)
    .sort();

  if (libraryProjects.length === 0) {
    throw new Error('No library projects found in the workspace');
  }

  // Check if swagger-generator exists and make it the default
  const defaultProject = libraryProjects.includes('swagger-generator')
    ? 'swagger-generator'
    : libraryProjects[0];

  logger.info('📋 Available library projects:');
  libraryProjects.forEach((project, index) => {
    const marker = project === defaultProject ? '❯' : ' ';
    logger.info(`  ${marker} ${project}`);
  });

  // For now, return the default project
  // In a real implementation, you would use inquirer or similar for interactive selection
  logger.info(`🎯 Using default project: ${defaultProject}`);
  return defaultProject;
}

async function validateInputs(
  tree: Tree,
  options: SwaggerGenGeneratorSchema,
): Promise<void> {
  // Check if swagger file exists
  if (!existsSync(options.swaggerPath)) {
    throw new Error(`Swagger file not found: ${options.swaggerPath}`);
  }

  // Check if target project exists
  const projects = getProjects(tree);
  if (!projects.has(options.targetProject)) {
    throw new Error(`Project '${options.targetProject}' not found`);
  }

  // Verify it's a library project
  const projectConfig = readProjectConfiguration(tree, options.targetProject);
  if (projectConfig.projectType !== 'library') {
    throw new Error(`Project '${options.targetProject}' is not a library`);
  }
}

async function extractMicroserviceName(swaggerPath: string): Promise<string> {
  try {
    const swaggerContent = readFileSync(swaggerPath, 'utf-8');
    const swaggerSpec: SwaggerSpec = JSON.parse(swaggerContent);

    if (!swaggerSpec.info?.title) {
      throw new Error('Swagger file missing info.title property');
    }

    let title = swaggerSpec.info.title;

    // Remove prefix before first dash if it exists
    const dashIndex = title.indexOf('-');
    if (dashIndex > 0) {
      title = title.substring(dashIndex + 1);
    }

    // Clean up the name to be a valid directory name
    return title
      .toLowerCase()
      .replace(/[^a-z0-9-]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  } catch (error) {
    throw new Error(`Failed to parse swagger file: ${error.message}`);
  }
}

async function createEntryPoint(
  tree: Tree,
  targetProject: string,
  microserviceName: string,
): Promise<void> {
  logger.info(`📦 Creating new entry point: ${microserviceName}`);

  // Use Nx generator to create secondary entry point
  await librarySecondaryEntryPointGenerator(tree, {
    name: microserviceName,
    library: targetProject,
    skipModule: true,
  });
}

async function generateSwaggerFiles(
  tree: Tree,
  options: SwaggerGenGeneratorSchema,
  microserviceName: string,
  projectRoot: string,
): Promise<void> {
  logger.info(`🔧 Generating swagger files for ${microserviceName}...`);

  const outputDir = join(projectRoot, microserviceName, 'src', 'lib');
  const absoluteOutputDir = join(process.cwd(), outputDir);

  // Execute ng-swagger-gen
  const command = `npx ng-swagger-gen -i "${options.swaggerPath}" -o "${absoluteOutputDir}"`;

  try {
    execSync(command, { stdio: 'inherit' });
    logger.info('✅ ng-swagger-gen execution completed');
  } catch (error) {
    throw new Error(`Failed to execute ng-swagger-gen: ${error.message}`);
  }
}

async function updateIndexFile(
  tree: Tree,
  projectRoot: string,
  microserviceName: string,
): Promise<void> {
  const indexPath = join(projectRoot, microserviceName, 'src', 'index.ts');

  const indexContent = `export * from './lib/api-configuration';
export * from './lib/api.module';
export * from './lib/base-service';
export * from './lib/models';
export * from './lib/services';
export * from './lib/strict-http-response';
`;

  tree.write(indexPath, indexContent);
  logger.info(`📝 Updated index file: ${indexPath}`);
}

async function updateProjectConfig(
  tree: Tree,
  targetProject: string,
  microserviceName: string,
): Promise<void> {
  // This function would update project.json to include the new entry point
  // For now, we'll log that this step is needed
  logger.info(
    `📋 Entry point '${microserviceName}' created in project '${targetProject}'`,
  );
}

async function performDryRun(
  tree: Tree,
  options: SwaggerGenGeneratorSchema,
  microserviceName: string,
  entryPointExists: boolean,
): Promise<void> {
  logger.info('🔍 Dry run mode - showing what would be generated:');
  logger.info('');

  if (!entryPointExists) {
    logger.info(`📦 Would create new entry point: ${microserviceName}`);
  } else {
    logger.info(`📦 Would update existing entry point: ${microserviceName}`);
  }

  const projectConfig = readProjectConfiguration(tree, options.targetProject);
  const outputDir = join(projectConfig.root, microserviceName, 'src', 'lib');

  logger.info(`🔧 Would generate swagger files in: ${outputDir}`);
  logger.info(
    `📝 Would update index file: ${join(projectConfig.root, microserviceName, 'src', 'index.ts')}`,
  );
  logger.info('');
  logger.info('Files that would be generated by ng-swagger-gen:');
  logger.info('  - api-configuration.ts');
  logger.info('  - api.module.ts');
  logger.info('  - base-service.ts');
  logger.info('  - models.ts');
  logger.info('  - services.ts');
  logger.info('  - strict-http-response.ts');
  logger.info('  - models/ (directory with individual model files)');
  logger.info('  - services/ (directory with individual service files)');
  logger.info('');
  logger.info('To execute the generation, run the command without --dry-run');
}
