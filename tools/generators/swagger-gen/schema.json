{"$schema": "http://json-schema.org/schema", "$id": "SwaggerGenGenerator", "title": "Swagger Generator", "description": "Generate Angular services and types from Swagger/OpenAPI specifications", "type": "object", "properties": {"swaggerPath": {"type": "string", "description": "Path to the swagger.json file", "default": "./swagger.json", "$default": {"$source": "argv", "index": 0}}, "targetProject": {"type": "string", "description": "Target Nx library project name", "default": "swagger-generator", "x-prompt": {"message": "Which library project should be used for generation?", "type": "list", "items": {"$source": "projectNames", "filter": "isLibrary"}}}, "dryRun": {"type": "boolean", "description": "Show what would be generated without making changes", "default": false, "alias": "d"}, "force": {"type": "boolean", "description": "Overwrite existing files without prompting", "default": false, "alias": "f"}}, "required": ["swaggerPath", "targetProject"], "additionalProperties": false}