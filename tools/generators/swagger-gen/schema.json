{"$schema": "http://json-schema.org/draft-07/schema", "$id": "SwaggerGenGenerator", "title": "Swagger Generator", "description": "Generate Angular services and types from Swagger/OpenAPI specifications with automatic microservice detection and multi-entry point support", "type": "object", "properties": {"swaggerPath": {"type": "string", "description": "Path to the swagger.json or OpenAPI specification file", "default": "./swagger.json", "pattern": "^.*\\.(json|yaml|yml)$", "x-prompt": "What is the path to your swagger/OpenAPI file?", "$default": {"$source": "argv", "index": 0}}, "targetProject": {"type": "string", "description": "Target Nx library project where the generated services will be placed", "default": "swagger-generator", "x-prompt": {"message": "Which library project should be used for generation?", "type": "list"}, "x-priority": "important"}, "dryRun": {"type": "boolean", "description": "Preview what would be generated without making any actual changes to the filesystem", "default": false, "alias": "d", "x-prompt": "Would you like to preview the changes without applying them?"}, "force": {"type": "boolean", "description": "Overwrite existing files without prompting for confirmation", "default": false, "alias": "f", "x-prompt": "Overwrite existing files without prompting?"}, "skipInstall": {"type": "boolean", "description": "<PERSON>p installing ng-swagger-gen if it's not already installed", "default": false, "x-prompt": "Skip installing ng-swagger-gen if missing?"}, "verbose": {"type": "boolean", "description": "Enable verbose logging for detailed output during generation", "default": false, "alias": "v"}}, "required": ["swaggerPath"], "additionalProperties": false}