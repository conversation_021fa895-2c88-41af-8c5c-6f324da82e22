# Swagger Generator Usage Guide

## Quick Start

```bash
# Basic usage with default settings
nx generate swagger-gen

# Specify a custom swagger file
nx generate swagger-gen ./api/swagger.json

# Target a specific library project
nx generate swagger-gen --targetProject=my-api-lib

# Preview changes without executing
nx generate swagger-gen --dryRun
```

## Step-by-Step Tutorial

### 1. Prepare Your Swagger File

Ensure your swagger.json file has the required structure:

```json
{
  "openapi": "3.0.0",
  "info": {
    "title": "mlsrv-neoshare-ai",
    "description": "AI service for document processing",
    "version": "1.0.0"
  },
  "paths": {
    // Your API endpoints
  }
}
```

**Important**: The `info.title` property is required and will be used to generate the microservice name.

### 2. Create or Select Target Library

```bash
# Create a new library for API clients (if needed)
nx generate @nx/angular:library swagger-generator

# Or use an existing library
nx show projects --type=library
```

### 3. Run the Generator

```bash
# Interactive mode - will prompt for project selection
nx generate swagger-gen ./swagger.json

# Non-interactive mode
nx generate swagger-gen ./swagger.json --targetProject=swagger-generator
```

### 4. Verify Generated Files

The generator creates the following structure:

```
libs/swagger-generator/
├── neoshare-ai/                    # Extracted from "mlsrv-neoshare-ai"
│   ├── ng-package.json
│   └── src/
│       ├── index.ts                # Public API exports
│       └── lib/
│           ├── api-configuration.ts
│           ├── api.module.ts
│           ├── base-service.ts
│           ├── models.ts
│           ├── services.ts
│           ├── strict-http-response.ts
│           ├── models/             # Individual model files
│           └── services/           # Individual service files
```

### 5. Use Generated Services

```typescript
// Import the generated API module
import { ApiModule } from '@workspace/swagger-generator/neoshare-ai';

// In your app module
@NgModule({
  imports: [
    ApiModule.forRoot({ rootUrl: 'https://api.example.com' })
  ]
})
export class AppModule {}

// Use generated services
import { SomeService } from '@workspace/swagger-generator/neoshare-ai';

@Component({...})
export class MyComponent {
  constructor(private apiService: SomeService) {}
  
  async loadData() {
    const data = await this.apiService.getData().toPromise();
    return data;
  }
}
```

## Advanced Usage

### Multiple Microservices

Generate services for multiple APIs:

```bash
# Generate for different services
nx generate swagger-gen ./auth-swagger.json --targetProject=api-clients
nx generate swagger-gen ./user-swagger.json --targetProject=api-clients
nx generate swagger-gen ./billing-swagger.json --targetProject=api-clients
```

This creates separate entry points:
- `@workspace/api-clients/auth`
- `@workspace/api-clients/user`
- `@workspace/api-clients/billing`

### Custom Library Structure

```bash
# Create specialized libraries
nx generate @nx/angular:library auth-api
nx generate @nx/angular:library user-api

# Generate services in specific libraries
nx generate swagger-gen ./auth-swagger.json --targetProject=auth-api
nx generate swagger-gen ./user-swagger.json --targetProject=user-api
```

### Updating Existing Services

When your API changes, simply re-run the generator:

```bash
# Download updated swagger file
curl -o swagger.json https://api.example.com/swagger.json

# Regenerate services (will update existing entry point)
nx generate swagger-gen ./swagger.json --targetProject=swagger-generator
```

### Dry Run for Safety

Always preview changes before applying:

```bash
# See what would be generated
nx generate swagger-gen ./swagger.json --dryRun

# Output shows:
# 📦 Would UPDATE existing entry point: neoshare-ai
# 🔧 Files to be generated by ng-swagger-gen:
#   📁 Output directory: libs/swagger-generator/neoshare-ai/src/lib
#   📄 Generated files:
#     🔄 UPDATE: api-configuration.ts
#     🔄 UPDATE: api.module.ts
#     ...
```

## Troubleshooting

### Common Issues

1. **"Swagger file not found"**
   ```bash
   # Check file path
   ls -la ./swagger.json
   
   # Use absolute path if needed
   nx generate swagger-gen /full/path/to/swagger.json
   ```

2. **"Project not found"**
   ```bash
   # List available projects
   nx show projects --type=library
   
   # Create library if needed
   nx generate @nx/angular:library my-api-lib
   ```

3. **"Missing info.title"**
   ```json
   {
     "info": {
       "title": "My API Service",  // ← Required
       "version": "1.0.0"
     }
   }
   ```

4. **"ng-swagger-gen execution failed"**
   ```bash
   # Install ng-swagger-gen if missing
   npm install ng-swagger-gen
   
   # Test manually
   npx ng-swagger-gen --help
   ```

### Debug Mode

```bash
# Run with verbose output
nx generate swagger-gen ./swagger.json --verbose

# Use dry-run to debug without changes
nx generate swagger-gen ./swagger.json --dryRun
```

## Best Practices

### 1. Organize by Domain

```
libs/
├── auth-api/           # Authentication services
├── user-api/           # User management
├── billing-api/        # Billing services
└── shared-types/       # Common types
```

### 2. Version Management

```bash
# Keep swagger files versioned
mkdir api-specs
curl -o api-specs/auth-v1.json https://auth-api.com/swagger.json
curl -o api-specs/user-v2.json https://user-api.com/swagger.json

# Generate with versioned files
nx generate swagger-gen ./api-specs/auth-v1.json --targetProject=auth-api
```

### 3. CI/CD Integration

```yaml
# .github/workflows/update-api-clients.yml
name: Update API Clients
on:
  schedule:
    - cron: '0 2 * * *'  # Daily at 2 AM

jobs:
  update:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Download latest swagger specs
        run: |
          curl -o swagger.json ${{ secrets.API_SWAGGER_URL }}
      - name: Generate API clients
        run: |
          nx generate swagger-gen ./swagger.json --targetProject=swagger-generator
      - name: Create PR if changes
        # ... PR creation logic
```

### 4. Type Safety

```typescript
// Use generated types for better type safety
import { UserDto, CreateUserRequest } from '@workspace/swagger-generator/user-api';

export class UserService {
  async createUser(request: CreateUserRequest): Promise<UserDto> {
    // TypeScript will enforce correct types
    return this.apiService.createUser(request).toPromise();
  }
}
```

## Configuration

### Generator Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `swaggerPath` | string | `./swagger.json` | Path to swagger specification |
| `targetProject` | string | `swagger-generator` | Target Nx library project |
| `dryRun` | boolean | `false` | Preview changes without applying |
| `force` | boolean | `false` | Overwrite files without prompting |

### ng-swagger-gen Configuration

The generator uses these ng-swagger-gen settings:

```json
{
  "ignoreUnusedModels": false,
  "removeStaleFiles": true,
  "modelIndex": true,
  "serviceIndex": true,
  "apiModule": true,
  "configuration": true,
  "baseService": true,
  "requestBuilder": false,
  "response": true
}
```

## Examples

See the [examples directory](./examples/) for complete working examples:

- [Basic API Client](./examples/basic-api-client/)
- [Multi-Service Setup](./examples/multi-service/)
- [Custom Configuration](./examples/custom-config/)
- [CI/CD Integration](./examples/cicd-integration/)
