# Nx Console Compatibility Fix

## Problem

The original error `"Error sending request to Nx Language Server: Error: Request nx/generatorOptions failed with message: (t.items || []).forEach is not a function"` was caused by complex schema configurations that the Nx Language Server couldn't parse properly.

## Root Cause

The issue was in the `x-prompt` configuration for the `targetProject` parameter:

```json
// ❌ Problematic configuration
"x-prompt": {
  "message": "Which library project should be used for generation?",
  "type": "list",
  "items": {
    "$source": "projectNames",
    "filter": "isLibrary"
  }
}
```

The Nx Language Server expected `items` to be an array, but we provided an object with `$source` and `filter` properties, causing the `forEach` error.

## Solution

### 1. Simplified Schema Configuration

Removed complex `x-prompt` configurations and simplified to basic prompts:

```json
// ✅ Fixed configuration
"x-prompt": {
  "message": "Which library project should be used for generation?",
  "type": "list"
}
```

### 2. Moved Logic to Generator Code

Instead of relying on schema-based project filtering, implemented the project selection logic directly in the generator:

```typescript
async function selectTargetProject(tree: Tree): Promise<string> {
  const projects = getProjects(tree);
  const libraryProjects = Array.from(projects.entries())
    .filter(([_, config]) => config.projectType === 'library')
    .map(([name, _]) => name)
    .sort();

  // Try to use inquirer for interactive selection
  try {
    const inquirer = require('inquirer');
    const answer = await inquirer.prompt([
      {
        type: 'list',
        name: 'project',
        message: 'Which library project should be used for generation?',
        choices: libraryProjects,
        default: defaultProject,
      },
    ]);
    return answer.project;
  } catch (error) {
    // Fallback to default if inquirer is not available
    return defaultProject;
  }
}
```

### 3. Removed Problematic Schema Properties

Eliminated schema properties that could cause parsing issues:

- Removed `cli: "nx"` and `outputCapture: "direct-nodejs"`
- Simplified `x-prompt` configurations to basic strings or simple objects
- Removed complex `when` conditions and `items` configurations
- Removed `examples` array that might cause parsing issues

## Changes Made

### Schema Simplifications

1. **Simplified prompts** - Changed from complex objects to simple strings
2. **Removed conditional logic** - Eliminated `when` clauses that could cause issues
3. **Removed dynamic items** - No more `$source` and `filter` configurations
4. **Cleaned up metadata** - Removed potentially problematic properties

### Generator Enhancements

1. **Added inquirer support** - Interactive project selection when available
2. **Graceful fallback** - Default project selection when inquirer is not available
3. **Better error handling** - More specific error types and messages
4. **Enhanced logging** - Verbose mode for debugging

## Final Schema Structure

```json
{
  "$schema": "http://json-schema.org/draft-07/schema",
  "$id": "SwaggerGenGenerator",
  "title": "Swagger Generator",
  "description": "Generate Angular services and types from Swagger/OpenAPI specifications",
  "type": "object",
  "properties": {
    "swaggerPath": {
      "type": "string",
      "description": "Path to the swagger.json or OpenAPI specification file",
      "default": "./swagger.json",
      "pattern": "^.*\\.(json|yaml|yml)$",
      "x-prompt": "What is the path to your swagger/OpenAPI file?"
    },
    "targetProject": {
      "type": "string",
      "description": "Target Nx library project where the generated services will be placed",
      "default": "swagger-generator",
      "x-prompt": {
        "message": "Which library project should be used for generation?",
        "type": "list"
      }
    },
    "dryRun": {
      "type": "boolean",
      "description": "Preview what would be generated without making changes",
      "default": false,
      "x-prompt": "Would you like to preview the changes without applying them?"
    },
    "force": {
      "type": "boolean", 
      "description": "Overwrite existing files without prompting",
      "default": false,
      "x-prompt": "Overwrite existing files without prompting?"
    },
    "skipInstall": {
      "type": "boolean",
      "description": "Skip installing ng-swagger-gen if missing",
      "default": false,
      "x-prompt": "Skip installing ng-swagger-gen if missing?"
    },
    "verbose": {
      "type": "boolean",
      "description": "Enable verbose logging for detailed output",
      "default": false
    }
  },
  "required": ["swaggerPath"],
  "additionalProperties": false
}
```

## Testing

### Schema Validation

Created a test script (`tools/test-schema.js`) that validates:

- ✅ JSON syntax is valid
- ✅ Schema structure is correct
- ✅ No problematic patterns detected
- ✅ All properties are properly defined

### Generator Functionality

Verified that the generator still works correctly:

- ✅ Dry-run mode functions properly
- ✅ Verbose logging works
- ✅ Project selection logic works
- ✅ All existing functionality preserved

## Best Practices for Nx Console Compatibility

### 1. Keep Schema Simple

- Use basic `x-prompt` configurations
- Avoid complex nested objects
- Prefer simple strings over complex configurations

### 2. Implement Logic in Code

- Move complex logic from schema to generator code
- Use runtime project discovery instead of schema-based filtering
- Handle interactive selection programmatically

### 3. Test Schema Parsing

- Validate JSON syntax regularly
- Test with Nx Console extension
- Create automated schema validation tests

### 4. Graceful Degradation

- Provide fallbacks when interactive features aren't available
- Handle missing dependencies gracefully
- Ensure generator works in both CLI and UI modes

## Verification

To verify the fix works:

1. **Restart VS Code** to reload the Nx Console extension
2. **Open Command Palette** (`Ctrl+Shift+P` / `Cmd+Shift+P`)
3. **Run "Nx: Generate"**
4. **Select "swagger-gen"** from the list
5. **Verify the form loads** without errors
6. **Test parameter input** and validation

The generator should now work seamlessly in both command-line and Nx Console UI modes.
