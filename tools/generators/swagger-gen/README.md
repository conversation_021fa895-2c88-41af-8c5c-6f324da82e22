# Swagger Generator

An Nx workspace generator that integrates with ng-swagger-gen to automatically generate Angular services, TypeScript types, modules, and related files for API consumption.

## Features

- 🚀 **Automatic microservice detection** - Extracts microservice name from swagger.json `info.title`
- 📦 **Multi-entry point support** - Creates separate entry points for each microservice
- 🔧 **Interactive project selection** - Choose from available Nx library projects
- 🔍 **Dry-run mode** - Preview changes before applying them
- ✅ **Error handling** - Comprehensive validation and error messages
- 📝 **Index file management** - Automatically creates/updates index.ts exports

## Usage

### Basic Usage

```bash
# Generate from swagger.json in current directory to swagger-generator library
nx generate @nx/workspace:swagger-gen

# Specify custom swagger file path
nx generate @nx/workspace:swagger-gen ./path/to/swagger.json

# Target a specific library project
nx generate @nx/workspace:swagger-gen --targetProject=my-api-lib

# Dry run to see what would be generated
nx generate @nx/workspace:swagger-gen --dryRun
```

### Interactive Mode

When you run the generator without specifying a target project, it will prompt you to select from available library projects:

```bash
nx generate @nx/workspace:swagger-gen
? Which library project should be used for generation? (Use arrow keys)
❯ swagger-generator
  my-api-lib
  shared-types
```

## Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `swaggerPath` | string | `./swagger.json` | Path to the swagger.json file |
| `targetProject` | string | `swagger-generator` | Target Nx library project name |
| `dryRun` | boolean | `false` | Show what would be generated without making changes |
| `force` | boolean | `false` | Overwrite existing files without prompting |

## How It Works

1. **Validates inputs** - Checks if swagger file exists and target project is a valid library
2. **Extracts microservice name** - Parses `info.title` from swagger.json and removes prefix before first dash
3. **Creates/updates entry point** - Uses `@nx/angular:library-secondary-entry-point` if needed
4. **Generates files** - Executes `ng-swagger-gen` to create Angular services and types
5. **Updates index** - Creates/updates index.ts with required exports
6. **Updates project config** - Registers new entry points in build configuration

## Generated Structure

For a swagger file with `info.title: "mlsrv-neoshare-ai"`, the generator creates:

```
libs/swagger-generator/
├── neoshare-ai/
│   ├── ng-package.json
│   └── src/
│       ├── index.ts                 # Exports all generated modules
│       └── lib/
│           ├── api-configuration.ts
│           ├── api.module.ts
│           ├── base-service.ts
│           ├── models.ts
│           ├── services.ts
│           ├── strict-http-response.ts
│           ├── models/              # Individual model files
│           └── services/            # Individual service files
```

## Index File Exports

The generated index.ts file exports exactly these modules:

```typescript
export * from './lib/api-configuration';
export * from './lib/api.module';
export * from './lib/base-service';
export * from './lib/models';
export * from './lib/services';
export * from './lib/strict-http-response';
```

## Error Handling

The generator handles common error scenarios:

- ❌ Swagger file not found
- ❌ Invalid JSON in swagger file
- ❌ Missing `info.title` property
- ❌ Target project doesn't exist
- ❌ Target project is not a library
- ❌ ng-swagger-gen execution failures

## Examples

### Generate from remote swagger file

```bash
# Download swagger file first
curl -o swagger.json https://api.example.com/swagger.json

# Generate services
nx generate @nx/workspace:swagger-gen
```

### Multiple microservices

```bash
# Generate for different microservices
nx generate @nx/workspace:swagger-gen ./auth-service-swagger.json
nx generate @nx/workspace:swagger-gen ./user-service-swagger.json
nx generate @nx/workspace:swagger-gen ./billing-service-swagger.json
```

### Custom library project

```bash
# Create a new library for API clients
nx generate @nx/angular:library api-clients

# Generate swagger services in the new library
nx generate @nx/workspace:swagger-gen --targetProject=api-clients
```

## Requirements

- Nx workspace with Angular
- ng-swagger-gen package installed
- Valid swagger.json/OpenAPI specification file
- Target library project must exist and be of type 'library'
