# Workspace Generators

This directory contains custom Nx workspace generators for automating common development tasks.

## Available Generators

### swagger-gen

Automatically generates Angular services and TypeScript types from Swagger/OpenAPI specifications.

**Features:**
- 🚀 Automatic microservice detection from swagger.json `info.title`
- 📦 Multi-entry point support for different microservices
- 🔧 Interactive project selection
- 🔍 Dry-run mode for previewing changes
- ✅ Comprehensive error handling
- 📝 Automatic index file management

**Quick Start:**
```bash
# Basic usage
nx generate swagger-gen

# With custom swagger file
nx generate swagger-gen ./api/swagger.json

# Target specific project
nx generate swagger-gen --targetProject=my-api-lib

# Preview changes
nx generate swagger-gen --dryRun
```

**Documentation:**
- [README](./swagger-gen/README.md) - Overview and features
- [USAGE](./swagger-gen/USAGE.md) - Detailed usage guide and examples
- [Schema](./swagger-gen/schema.json) - Parameter definitions

## Installation

The generators are automatically available in your Nx workspace. No additional installation required.

## Usage

### Command Line

```bash
# List available generators
nx generate --help

# Run a specific generator
nx generate <generator-name> [options]

# Get help for a specific generator
nx generate <generator-name> --help
```

### Interactive Mode

Most generators support interactive mode where you'll be prompted for required parameters:

```bash
nx generate swagger-gen
? Which library project should be used for generation? (Use arrow keys)
❯ swagger-generator
  my-api-lib
  shared-types
```

### Programmatic Usage

```typescript
import { Tree } from '@nx/devkit';
import swaggerGenGenerator from './generators/swagger-gen';

// Use in other generators or scripts
await swaggerGenGenerator(tree, {
  swaggerPath: './swagger.json',
  targetProject: 'api-clients',
  dryRun: false
});
```

## Development

### Adding New Generators

1. Create a new directory under `tools/generators/`
2. Add the required files:
   - `index.ts` - Main generator implementation
   - `schema.json` - Parameter schema
   - `README.md` - Documentation

3. Register in `collection.json`:
```json
{
  "schematics": {
    "my-generator": {
      "factory": "./my-generator/index",
      "schema": "./my-generator/schema.json",
      "description": "Description of my generator"
    }
  }
}
```

### Testing Generators

```bash
# Run generator tests
nx test tools

# Test specific generator
npm test -- --testPathPattern=swagger-gen
```

### Generator Structure

```
tools/generators/
├── collection.json           # Generator registry
├── swagger-gen/             # Swagger generator
│   ├── index.ts            # Main implementation
│   ├── index.spec.ts       # Unit tests
│   ├── schema.json         # Parameter schema
│   ├── README.md           # Overview
│   └── USAGE.md            # Detailed guide
└── README.md               # This file
```

## Best Practices

### Generator Implementation

1. **Use TypeScript** for type safety and better IDE support
2. **Validate inputs** early and provide clear error messages
3. **Support dry-run mode** for previewing changes
4. **Use Nx devkit utilities** for file operations and project management
5. **Provide comprehensive logging** with clear status indicators

### Error Handling

```typescript
try {
  // Generator logic
} catch (error) {
  logger.error(`❌ Generator failed: ${error.message}`);
  logger.error('💡 Solutions:');
  logger.error('  - Check input parameters');
  logger.error('  - Verify file permissions');
  throw error;
}
```

### User Experience

1. **Interactive prompts** for better usability
2. **Clear progress indicators** with emojis and colors
3. **Helpful error messages** with suggested solutions
4. **Dry-run support** for safe previewing
5. **Comprehensive documentation** with examples

## Configuration

### Workspace Configuration

The generators are configured in `nx.json`:

```json
{
  "cli": {
    "collection": "./tools/generators/collection.json"
  }
}
```

### Generator Defaults

Set default values for generator options:

```json
{
  "generators": {
    "swagger-gen": {
      "targetProject": "swagger-generator",
      "dryRun": false
    }
  }
}
```

## Troubleshooting

### Common Issues

1. **Generator not found**
   - Check `collection.json` registration
   - Verify file paths are correct
   - Ensure TypeScript compilation succeeds

2. **Import errors**
   - Check `@nx/devkit` version compatibility
   - Verify all dependencies are installed
   - Use correct import paths

3. **Permission errors**
   - Check file system permissions
   - Run from workspace root directory
   - Verify output directories are writable

### Debug Mode

```bash
# Run with verbose logging
nx generate swagger-gen --verbose

# Use dry-run to debug without changes
nx generate swagger-gen --dryRun

# Check generator registration
nx generate --help
```

## Contributing

1. Follow the existing code style and patterns
2. Add comprehensive tests for new generators
3. Update documentation for any changes
4. Test generators in different scenarios
5. Provide clear commit messages

## Resources

- [Nx Devkit Documentation](https://nx.dev/packages/devkit)
- [Creating Custom Generators](https://nx.dev/extending-nx/recipes/local-generators)
- [Generator Examples](https://github.com/nrwl/nx/tree/master/packages)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
