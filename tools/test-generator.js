#!/usr/bin/env node

const { createTreeWithEmptyWorkspace } = require('@nx/devkit/testing');
const { libraryGenerator } = require('@nx/angular/generators');
const generator = require('./generators/swagger-gen/index.js');

async function testGenerator() {
  console.log('🧪 Testing Swagger Generator...');
  
  try {
    // Create a test workspace
    const tree = createTreeWithEmptyWorkspace();
    
    // Create a test library
    await libraryGenerator(tree, {
      name: 'swagger-generator',
      skipModule: true,
    });
    
    // Create a mock swagger file
    const mockSwagger = {
      openapi: '3.0.0',
      info: {
        title: 'mlsrv-neoshare-ai',
        version: '1.0.0',
        description: 'Test API service'
      },
      paths: {
        '/test': {
          get: {
            summary: 'Test endpoint',
            responses: {
              '200': {
                description: 'Success'
              }
            }
          }
        }
      }
    };
    
    tree.write('./swagger.json', JSON.stringify(mockSwagger, null, 2));
    
    // Test the generator in dry-run mode
    console.log('📋 Running generator in dry-run mode...');
    await generator.default(tree, {
      swaggerPath: './swagger.json',
      targetProject: 'swagger-generator',
      dryRun: true
    });
    
    console.log('✅ Generator test completed successfully!');
    
  } catch (error) {
    console.error('❌ Generator test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

testGenerator();
