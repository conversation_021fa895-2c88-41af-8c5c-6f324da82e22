# Components Library

⚠️ **DEPRECATED**: This library should not be improved anymore. It contains legacy shared UI components for the FinCloud platform.

## Component Placement Guidelines

- **Shared components WITH business logic** → Move to `neoshare` library
- **Shared components WITHOUT business logic** → Use `@fincloud/ui` library
- **This library** → Legacy components only, no new additions

## Architecture Role

This library sits in the middle of the dependency hierarchy:

- **Depends on**: state, core, types, swagger-generator
- **Used by**: neoshare feature modules, main app
- **Purpose**: Provides reusable UI components with integrated state management

## Component Organization

Components are organized into specialized modules, each following Angular best practices:

- Standalone components (Angular 17)
- State-driven data flow via NgRx
- Bootstrap 5.3 + custom styling
- TypeScript with strict typing
- i18n support (German/English)

## Key Features

- **Data Grids**: AG Grid Enterprise integration
- **Form Controls**: Custom form components with validation
- **Navigation**: Routing and menu components
- **Modals & Dialogs**: Reusable popup components
- **Charts & Visualization**: Financial KPI displays
- **Document Management**: Upload and display components
- **Maps Integration**: Azure Maps components

## Dependencies

Components integrate with:

- NgRx state management
- Core business logic services
- Type definitions
- Auto-generated API clients
