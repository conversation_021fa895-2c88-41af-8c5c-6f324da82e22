{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s", "!{projectRoot}/cypress/**/*", "!{projectRoot}/**/*.cy.[jt]s?(x)", "!{projectRoot}/cypress.config.[jt]s"], "sharedGlobals": []}, "targetDefaults": {"@angular-devkit/build-angular:browser": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "@nx/eslint:lint": {"cache": true, "inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/.eslintignore", "{workspaceRoot}/eslint.config.js"]}, "@nx/jest:jest": {"cache": true, "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "options": {"passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "@nx/angular:webpack-browser": {"inputs": ["production", "^production", {"env": "NX_MF_DEV_SERVER_STATIC_REMOTES"}]}, "@nx/angular:package": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}}, "plugins": [{"plugin": "@nx/cypress/plugin", "options": {"targetName": "e2e", "openTargetName": "open-cypress", "componentTestingTargetName": "component-test", "ciTargetName": "e2e-ci"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}], "generators": {"@nx/angular:application": {"e2eTestRunner": "cypress", "linter": "eslint", "style": "scss", "unitTestRunner": "jest"}, "@nx/angular:library": {"linter": "eslint", "unitTestRunner": "jest"}, "@nx/angular:component": {"style": "scss", "changeDetection": "OnPush"}}, "workspaceLayout": {"appsDir": "apps", "libsDir": "libs"}, "cli": {"collection": "./tools/generators/collection.json"}, "workspaceGenerators": {"swagger-gen": "./tools/generators/swagger-gen"}}